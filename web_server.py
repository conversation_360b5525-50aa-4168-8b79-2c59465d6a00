#!/usr/bin/env python3
"""
Unified web server for Mail_Auto combining the processing backend with web portal.
Serves both customer dashboard and admin interface.
Development mode: Frontend runs on Vite dev server (port 5173), backend on Flask (port 5000)
Production mode: Flask serves built frontend files
"""

from flask import Flask, jsonify, request, send_from_directory, render_template_string
from flask_cors import CORS
import os
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Import your existing Mail_Auto components
from core.tenant_loader import list_tenants
from core.tracking import get_tracking_service, DocumentAnalytics
from core.tracking.models import ProcessingStatus, OperationType
from core.mailbox_manager import MailboxConfigManager
from core.config import config_manager

def filter_tenants_by_mode(tenants, dev_mode=False):
    """
    Filter tenants based on development mode.

    Args:
        tenants: List of tenant tuples from list_tenants()
        dev_mode: Boolean indicating if development mode is active

    Returns:
        Filtered list of tenants
    """
    if not dev_mode:
        # Production mode: exclude development/test tenants
        return [t for t in tenants if not any(keyword in t[0].lower()
                for keyword in ['prototype', 'dev', 'test', 'demo', 'sandbox', 'local'])]

    # Development mode: prioritize development tenants
    dev_keywords = ['prototype', 'dev', 'test', 'demo', 'sandbox', 'local']
    dev_tenants = [t for t in tenants if any(keyword in t[0].lower() for keyword in dev_keywords)]

    if dev_tenants:
        return dev_tenants

    # If no explicit dev tenants found, check for personal accounts (outlook.com, gmail.com, etc.)
    personal_domains = ['outlook.com', 'gmail.com', 'hotmail.com', 'live.com']
    personal_tenants = []

    for tenant_name, creds_source, config_dict, token_cache_source in tenants:
        # Check if tenant config contains personal email domains
        if config_dict:
            mailboxes = config_dict.get('mailboxes', {})
            # In the config, mailbox emails are used as keys
            for email_key in mailboxes.keys():
                if any(domain in email_key.lower() for domain in personal_domains):
                    personal_tenants.append((tenant_name, creds_source, config_dict, token_cache_source))
                    break

    if personal_tenants:
        return personal_tenants

    # Fallback: return first tenant for development testing
    return tenants[:1] if tenants else []

app = Flask(__name__)

# Configure CORS for development (allow Vite dev server)
CORS(app, origins=["http://localhost:5173", "http://127.0.0.1:5173"])

# Environment detection
DEVELOPMENT_MODE = os.environ.get('FLASK_ENV') == 'development' or os.environ.get('NODE_ENV') == 'development'

# Simple development dashboard (fallback when React app not built)
SIMPLE_DEV_DASHBOARD = """
<!DOCTYPE html>
<html>
<head>
    <title>Mail_Auto Development Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6">
            <p class="font-bold">Development Mode</p>
            <p>React app not built yet. Build it with: <code>cd core/web_portal && npm run build</code></p>
        </div>

        <h1 class="text-3xl font-bold text-gray-900 mb-8">Mail_Auto Development Dashboard</h1>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">System Status</h3>
                <div id="system-status" class="text-2xl font-bold text-green-600">Loading...</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Active Tenants</h3>
                <div id="tenant-count" class="text-2xl font-bold text-blue-600">-</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Documents Processed</h3>
                <div id="document-count" class="text-2xl font-bold text-purple-600">-</div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Success Rate</h3>
                <div id="success-rate" class="text-2xl font-bold text-green-600">-</div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="flex gap-4">
                <button id="refresh-data" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                    Refresh Data
                </button>
                <a href="/api/health" target="_blank" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 inline-block">
                    View API Health
                </a>
            </div>
        </div>
    </div>

    <script>
        async function loadData() {
            try {
                const health = await axios.get('/api/health');
                document.getElementById('system-status').textContent = 'Online';
                document.getElementById('tenant-count').textContent = health.data.tenants_count;

                const stats = await axios.get('/api/dashboard/stats');
                document.getElementById('document-count').textContent = stats.data.total_documents;
                document.getElementById('success-rate').textContent = stats.data.success_rate.toFixed(1) + '%';
            } catch (error) {
                document.getElementById('system-status').textContent = 'Offline';
                console.error('Error loading data:', error);
            }
        }

        document.getElementById('refresh-data').addEventListener('click', loadData);
        loadData();
        setInterval(loadData, 30000);
    </script>
</body>
</html>
"""

# Global variables for background processing
processing_thread = None
processing_active = False

def background_email_processor():
    """Background thread that runs the email processing loop"""
    global processing_active
    from main import process_tenants
    
    while processing_active:
        try:
            print(f"🔄 Background processing cycle - {datetime.now()}")
            process_tenants()
            time.sleep(300)  # 5 minutes between cycles
        except Exception as e:
            print(f"❌ Background processing error: {e}")
            time.sleep(60)  # Wait 1 minute on error

# ============================================================================
# API ENDPOINTS FOR WEB PORTAL
# ============================================================================

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    # Get development mode from query parameter
    dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'
    tenants = list_tenants()
    tenants = filter_tenants_by_mode(tenants, dev_mode)

    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "processing_active": processing_active,
        "tenants_count": len(tenants)
    })

@app.route('/api/dashboard/stats')
def dashboard_stats():
    """Get dashboard statistics for current user/tenant"""
    # Get development mode from query parameter
    dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

    tenants = list_tenants()
    tenants = filter_tenants_by_mode(tenants, dev_mode)
    total_tenants = len(tenants)

    # Initialize tracking for each tenant and aggregate stats
    total_docs = 0
    successful_docs = 0
    failed_docs = 0

    for tenant_name, _, tenant_cfg, _ in tenants:
        try:
            # Initialize tracking for this specific tenant
            from core.tracking.config import setup_tracking_for_tenant
            tracking_config = setup_tracking_for_tenant(tenant_cfg)

            if tracking_config.enabled:
                tracking_service = get_tracking_service()

                # Get all events for this tenant
                all_events = tracking_service.db.get_recent_events(tenant_name, limit=1000)

                for event in all_events:
                    total_docs += 1
                    if event.status.value == 'SUCCESS':
                        successful_docs += 1
                    elif event.status.value in ['FAILED', 'ERROR']:
                        failed_docs += 1
        except Exception as e:
            print(f"⚠️ Error getting stats for tenant {tenant_name}: {e}")
            continue

    if total_docs == 0:
        return jsonify({
            "total_documents": 0,
            "successful_documents": 0,
            "failed_documents": 0,
            "active_tenants": total_tenants,
            "processing_enabled": False,
            "success_rate": 0
        })
    
    # Aggregate stats from all tenants (for admin view)
    total_docs = 0
    successful_docs = 0
    failed_docs = 0
    
    for tenant_name, _, tenant_cfg, _ in tenants:
        try:
            # Initialize tracking for this specific tenant
            from core.tracking.config import setup_tracking_for_tenant
            tracking_config = setup_tracking_for_tenant(tenant_cfg)

            if tracking_config.enabled:
                tracking_service = get_tracking_service()

                # Get last 30 days stats using date range
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=30)

                daily_stats = tracking_service.get_date_range_stats(tenant_name, start_date, end_date)

                # Aggregate the daily stats
                for stat in daily_stats:
                    total_docs += stat.total_documents
                    successful_docs += stat.successful_documents
                    failed_docs += stat.failed_documents

        except Exception as e:
            print(f"Error getting stats for {tenant_name}: {e}")
            continue
    
    return jsonify({
        "total_documents": total_docs,
        "successful_documents": successful_docs,
        "failed_documents": failed_docs,
        "active_tenants": total_tenants,
        "processing_enabled": True,
        "success_rate": (successful_docs / total_docs * 100) if total_docs > 0 else 0
    })

@app.route('/api/dashboard/recent-activity')
def recent_activity():
    """Get recent document processing activity"""
    try:
        # Get development mode from query parameter
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        tenants = list_tenants()
        tenants = filter_tenants_by_mode(tenants, dev_mode)

        recent_events = []

        for tenant_name, _, tenant_cfg, _ in tenants:
            try:
                # Initialize tracking for this specific tenant
                from core.tracking.config import setup_tracking_for_tenant
                tracking_config = setup_tracking_for_tenant(tenant_cfg)

                if tracking_config.enabled:
                    tracking_service = get_tracking_service()
                    events = tracking_service.db.get_recent_events(tenant_name, limit=10)

                    for event in events:
                        recent_events.append({
                            "id": f"{tenant_name}_{event.filename}_{event.processing_date}",
                            "tenant": tenant_name,
                            "filename": event.filename,
                            "document_type": event.document_type,
                            "status": event.status.value,
                            "processing_date": event.processing_date.isoformat(),
                            "mailbox": event.mailbox_email,
                            "upload_folder": event.upload_folder,
                            "error_message": event.error_message
                        })
            except Exception as e:
                print(f"⚠️ Error getting recent activity for tenant {tenant_name}: {e}")
                continue

        # Sort by processing date (most recent first)
        recent_events.sort(key=lambda x: x["processing_date"], reverse=True)

        return jsonify(recent_events[:20])  # Return top 20 most recent

    except Exception as e:
        print(f"❌ Error getting recent activity: {e}")
        return jsonify([])

@app.route('/api/dashboard/live-stats')
def live_stats():
    """Get live processing statistics for real-time dashboard updates"""
    # Get development mode from query parameter
    dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

    tenants = list_tenants()
    tenants = filter_tenants_by_mode(tenants, dev_mode)

    if not tenants:
        return jsonify({
            "total_documents_today": 0,
            "successful_today": 0,
            "failed_today": 0,
            "processing_rate_per_hour": 0,
            "last_processed": None,
            "active_tenants": 0,
            "success_rate_today": 0
        })

    try:
        from datetime import datetime, timedelta
        from core.tracking.config import setup_tracking_for_tenant

        today = datetime.now().date()
        tenants = list_tenants()
        tenants = filter_tenants_by_mode(tenants, dev_mode)

        total_today = 0
        successful_today = 0
        failed_today = 0
        last_processed = None

        for tenant_name, _, tenant_cfg, _ in tenants:
            try:
                # Initialize tracking for this specific tenant
                tracking_config = setup_tracking_for_tenant(tenant_cfg)

                if tracking_config.enabled:
                    tracking_service = get_tracking_service()

                    # Get today's events for this tenant
                    today_events = tracking_service.db.get_events_by_date_range(tenant_name, today, today)

                    for event in today_events:
                        # Only count full_processing events to avoid duplicates
                        if event.operation_type.value == 'full_processing':
                            total_today += 1
                            if event.status.value == 'success':
                                successful_today += 1
                            elif event.status.value in ['failed', 'error']:
                                failed_today += 1

                        # Track most recent processing time
                        if last_processed is None or event.processing_date > last_processed:
                            last_processed = event.processing_date
            except Exception as e:
                print(f"⚠️ Error getting live stats for tenant {tenant_name}: {e}")
                continue

        # Calculate processing rate (documents per hour for today)
        now = datetime.now()
        hours_elapsed = (now - datetime.combine(today, datetime.min.time())).total_seconds() / 3600
        processing_rate = total_today / hours_elapsed if hours_elapsed > 0 else 0

        return jsonify({
            "total_documents_today": total_today,
            "successful_today": successful_today,
            "failed_today": failed_today,
            "processing_rate_per_hour": round(processing_rate, 2),
            "last_processed": last_processed.isoformat() if last_processed else None,
            "active_tenants": len(tenants),
            "success_rate_today": (successful_today / total_today * 100) if total_today > 0 else 0
        })

    except Exception as e:
        print(f"❌ Error getting live stats: {e}")
        tenants = list_tenants()
        tenants = filter_tenants_by_mode(tenants, dev_mode)
        return jsonify({
            "total_documents_today": 0,
            "successful_today": 0,
            "failed_today": 0,
            "processing_rate_per_hour": 0,
            "last_processed": None,
            "active_tenants": len(tenants),
            "success_rate_today": 0
        })

@app.route('/api/tenant/<tenant_name>/recent-activity')
def tenant_recent_activity(tenant_name):
    """Get recent activity for a specific tenant"""
    tracking_service = get_tracking_service()
    if not tracking_service.is_enabled():
        return jsonify([])

    try:
        events = tracking_service.db.get_recent_events(tenant_name, limit=20)
        activity = []

        for event in events:
            activity.append({
                "id": f"{tenant_name}_{event.filename}_{event.processing_date.isoformat()}",
                "filename": event.filename,
                "document_type": event.document_type,
                "status": event.status.value,
                "operation_type": event.operation_type.value,
                "processing_date": event.processing_date.isoformat(),
                "file_size": event.file_size,
                "mailbox": event.mailbox_email,
                "upload_folder": event.upload_folder,
                "processing_time_ms": event.processing_time_ms,
                "error_message": event.error_message,
                "notification_recipients": event.notification_recipients,
                "extracted_data": event.extracted_data
            })

        return jsonify(activity)

    except Exception as e:
        print(f"❌ Error getting tenant activity: {e}")
        return jsonify([])

@app.route('/api/dashboard/document-types')
def dashboard_document_types():
    """Get live document type distribution"""
    tracking_service = get_tracking_service()
    if not tracking_service.is_enabled():
        return jsonify([])

    try:
        from collections import defaultdict
        from datetime import datetime, timedelta

        # Get development mode from query parameter
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        tenants = list_tenants()
        document_types = defaultdict(int)

        # Filter tenants based on mode
        tenants = filter_tenants_by_mode(tenants, dev_mode)

        # Get document type distribution from last 30 days
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)

        for tenant_name, _, _, _ in tenants:
            try:
                # Initialize tracking for this specific tenant
                from core.tracking.config import setup_tracking_for_tenant
                tenant_cfg = next((cfg for name, _, cfg, _ in list_tenants() if name == tenant_name), None)
                if tenant_cfg:
                    tracking_config = setup_tracking_for_tenant(tenant_cfg)
                    if tracking_config.enabled:
                        events = tracking_service.db.get_events_by_date_range(tenant_name, start_date, end_date)
                        for event in events:
                            # Only count full_processing events to avoid duplicates
                            if event.operation_type.value == 'full_processing':
                                document_types[event.document_type] += 1
            except:
                continue

        # Convert to chart format
        chart_data = []
        colors = ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6', '#EF4444', '#06B6D4', '#84CC16', '#F97316']

        for i, (doc_type, count) in enumerate(document_types.items()):
            chart_data.append({
                'name': doc_type.title(),
                'value': count,
                'color': colors[i % len(colors)]
            })

        return jsonify(chart_data)

    except Exception as e:
        print(f"❌ Error getting document types: {e}")
        return jsonify([])

@app.route('/api/dashboard/daily-activity')
def dashboard_daily_activity():
    """Get daily processing activity for the last 7 days"""
    try:
        from collections import defaultdict
        from datetime import datetime, timedelta

        # Get development mode from query parameter
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        tenants = list_tenants()
        tenants = filter_tenants_by_mode(tenants, dev_mode)

        # Get last 7 days of data
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=6)

        daily_activity = defaultdict(lambda: {'date': '', 'documents': 0, 'successful': 0, 'failed': 0})

        for tenant_name, _, tenant_cfg, _ in tenants:
            try:
                # Initialize tracking for this specific tenant
                from core.tracking.config import setup_tracking_for_tenant
                tracking_config = setup_tracking_for_tenant(tenant_cfg)

                if tracking_config.enabled:
                    tracking_service = get_tracking_service()
                    # Only count full_processing events to avoid duplicates
                    events = tracking_service.db.get_events_by_date_range(tenant_name, start_date, end_date)

                    for event in events:
                        # Only count full_processing events to avoid counting the same document multiple times
                        if event.operation_type.value == 'full_processing':
                            date_str = event.processing_date.strftime('%Y-%m-%d')
                            daily_activity[date_str]['date'] = date_str
                            daily_activity[date_str]['documents'] += 1

                            if event.status.value == 'success':
                                daily_activity[date_str]['successful'] += 1
                            else:
                                daily_activity[date_str]['failed'] += 1
            except Exception as e:
                print(f"⚠️ Error getting daily activity for tenant {tenant_name}: {e}")
                continue

        # Convert to chart format with all 7 days
        chart_data = []
        for i in range(7):
            date = start_date + timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            data = daily_activity.get(date_str, {'date': date_str, 'documents': 0, 'successful': 0, 'failed': 0})
            chart_data.append({
                'date': date_str,
                'documents': data['documents'],
                'successful': data['successful'],
                'failed': data['failed']
            })

        return jsonify(chart_data)

    except Exception as e:
        print(f"❌ Error getting daily activity: {e}")
        return jsonify([])

@app.route('/api/dashboard/monthly-trend')
def dashboard_monthly_trend():
    """Get monthly processing trend for the last 6 months"""
    try:
        from collections import defaultdict
        from datetime import datetime, timedelta
        from calendar import monthrange

        # Get development mode from query parameter
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        tenants = list_tenants()
        tenants = filter_tenants_by_mode(tenants, dev_mode)

        # Get last 6 months of data
        now = datetime.now()
        monthly_trend = defaultdict(lambda: {'month': '', 'documents': 0, 'successful': 0, 'failed': 0})

        for tenant_name, _, tenant_cfg, _ in tenants:
            try:
                # Initialize tracking for this specific tenant
                from core.tracking.config import setup_tracking_for_tenant
                tracking_config = setup_tracking_for_tenant(tenant_cfg)

                if tracking_config.enabled:
                    tracking_service = get_tracking_service()

                    # Get events for the last 6 months
                    for months_ago in range(6):
                        if months_ago == 0:
                            # Current month
                            start_date = now.replace(day=1).date()
                            end_date = now.date()
                        else:
                            # Previous months
                            year = now.year
                            month = now.month - months_ago
                            if month <= 0:
                                month += 12
                                year -= 1

                            start_date = datetime(year, month, 1).date()
                            _, last_day = monthrange(year, month)
                            end_date = datetime(year, month, last_day).date()

                        month_str = start_date.strftime('%Y-%m')

                        # Only count full_processing events to avoid duplicates
                        events = tracking_service.db.get_events_by_date_range(tenant_name, start_date, end_date)

                        for event in events:
                            # Only count full_processing events to avoid counting the same document multiple times
                            if event.operation_type.value == 'full_processing':
                                monthly_trend[month_str]['month'] = month_str
                                monthly_trend[month_str]['documents'] += 1

                                if event.status.value == 'success':
                                    monthly_trend[month_str]['successful'] += 1
                                else:
                                    monthly_trend[month_str]['failed'] += 1

            except Exception as e:
                print(f"⚠️ Error getting monthly trend for tenant {tenant_name}: {e}")
                continue

        # Convert to chart format with all 6 months
        chart_data = []
        for months_ago in range(5, -1, -1):  # Reverse order for chronological display
            if months_ago == 0:
                month_date = now.replace(day=1)
            else:
                year = now.year
                month = now.month - months_ago
                if month <= 0:
                    month += 12
                    year -= 1
                month_date = datetime(year, month, 1)

            month_str = month_date.strftime('%Y-%m')
            data = monthly_trend.get(month_str, {'month': month_str, 'documents': 0, 'successful': 0, 'failed': 0})
            chart_data.append({
                'month': month_str,
                'month_name': month_date.strftime('%B %Y'),
                'documents': data['documents'],
                'successful': data['successful'],
                'failed': data['failed']
            })

        return jsonify(chart_data)

    except Exception as e:
        print(f"❌ Error getting monthly trend: {e}")
        return jsonify([])

@app.route('/api/admin/system-health')
def admin_system_health():
    """Get comprehensive system health metrics"""
    try:
        import psutil
        from datetime import datetime, timedelta

        # Get development mode
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Calculate uptime (approximate)
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime_seconds = (datetime.now() - boot_time).total_seconds()

        # Processing metrics
        tracking_service = get_tracking_service()
        tenants = list_tenants()

        # Filter tenants based on mode
        tenants = filter_tenants_by_mode(tenants, dev_mode)

        total_errors_today = 0
        total_processed_today = 0

        if tracking_service.is_enabled():
            today = datetime.now().date()
            for tenant_name, _, _, _ in tenants:
                try:
                    events = tracking_service.database.get_events_by_date_range(tenant_name, today, today)
                    for event in events:
                        total_processed_today += 1
                        if event.status.value in ['FAILED', 'ERROR']:
                            total_errors_today += 1
                except:
                    continue

        return jsonify({
            'cpu_usage': cpu_percent,
            'memory_usage': memory.percent,
            'disk_usage': disk.percent,
            'uptime_hours': uptime_seconds / 3600,
            'active_tenants': len(tenants),
            'documents_processed_today': total_processed_today,
            'errors_today': total_errors_today,
            'error_rate': (total_errors_today / total_processed_today * 100) if total_processed_today > 0 else 0,
            'processing_enabled': True,  # You can check actual processing status here
            'last_updated': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Error getting system health: {e}")
        tenants = list_tenants()
        tenants = filter_tenants_by_mode(tenants, dev_mode)
        return jsonify({
            'cpu_usage': 0,
            'memory_usage': 0,
            'disk_usage': 0,
            'uptime_hours': 0,
            'active_tenants': len(tenants),
            'documents_processed_today': 0,
            'errors_today': 0,
            'error_rate': 0,
            'processing_enabled': False,
            'last_updated': datetime.now().isoformat()
        })

@app.route('/api/admin/error-trends')
def admin_error_trends():
    """Get error trends over time"""
    tracking_service = get_tracking_service()
    if not tracking_service.is_enabled():
        return jsonify([])

    try:
        from datetime import datetime, timedelta
        from collections import defaultdict

        # Get development mode
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        tenants = list_tenants()
        tenants = filter_tenants_by_mode(tenants, dev_mode)

        # Get last 7 days of error data
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=7)

        daily_errors = defaultdict(lambda: {'date': '', 'errors': 0, 'total': 0})

        for tenant_name, _, _, _ in tenants:
            try:
                # Initialize tracking for this specific tenant
                tenant_cfg = next((cfg for name, _, cfg, _ in list_tenants() if name == tenant_name), None)
                if tenant_cfg:
                    from core.tracking.config import setup_tracking_for_tenant
                    tracking_config = setup_tracking_for_tenant(tenant_cfg)
                    if tracking_config.enabled:
                        events = tracking_service.db.get_events_by_date_range(tenant_name, start_date, end_date)
                        for event in events:
                            # Only count full_processing events to avoid duplicates
                            if event.operation_type.value == 'full_processing':
                                date_str = event.processing_date.strftime('%Y-%m-%d')
                                daily_errors[date_str]['date'] = date_str
                                daily_errors[date_str]['total'] += 1
                                if event.status.value in ['failed', 'error']:
                                    daily_errors[date_str]['errors'] += 1
            except:
                continue

        # Convert to chart format
        trend_data = []
        for i in range(7):
            date = end_date - timedelta(days=6-i)
            date_str = date.strftime('%Y-%m-%d')
            data = daily_errors.get(date_str, {'date': date_str, 'errors': 0, 'total': 0})
            trend_data.append({
                'date': date_str,
                'errors': data['errors'],
                'total': data['total'],
                'error_rate': (data['errors'] / data['total'] * 100) if data['total'] > 0 else 0
            })

        return jsonify(trend_data)

    except Exception as e:
        print(f"❌ Error getting error trends: {e}")
        return jsonify([])

@app.route('/api/admin/tenant-activity')
def admin_tenant_activity():
    """Get detailed tenant activity metrics"""
    tracking_service = get_tracking_service()

    try:
        from datetime import datetime, timedelta

        # Get development mode
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        tenants = list_tenants()
        print(f"🔍 DEBUG: Found {len(tenants)} total tenants: {[t[0] for t in tenants]}")

        tenants = filter_tenants_by_mode(tenants, dev_mode)
        print(f"🔍 DEBUG: After filtering for dev_mode={dev_mode}: {len(tenants)} tenants: {[t[0] for t in tenants]}")

        # If tracking is not enabled, still return tenant info but without activity data
        if not tracking_service.is_enabled():
            print("⚠️ DEBUG: Tracking service not enabled, returning basic tenant info")
            tenant_activity = []
            for tenant_name, storage_type, config_dict, _ in tenants:
                mailboxes = config_dict.get('mailboxes', {}) if config_dict else {}
                tenant_activity.append({
                    'tenant_name': tenant_name,
                    'storage_type': "key_vault" if storage_type == "key_vault" else "file",
                    'mailbox_count': len(mailboxes),
                    'documents_this_week': 0,
                    'successful_this_week': 0,
                    'failed_this_week': 0,
                    'success_rate': 0,
                    'last_activity': None,
                    'status': 'inactive',
                    'preferred_language': config_dict.get('defaults', {}).get('preferred_language', 'English') if config_dict else 'English',
                    'tracking_enabled': False
                })
            return jsonify(tenant_activity)

        tenant_activity = []
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)

        for tenant_name, creds_source, config_dict, _ in tenants:
            try:
                # Get mailboxes from config
                mailboxes = config_dict.get('mailboxes', {}) if config_dict else {}

                # Try to get tracking data, but don't fail if tracking service has issues
                recent_events = []
                week_events = []

                try:
                    # Initialize tracking for this specific tenant
                    from core.tracking.config import setup_tracking_for_tenant
                    tracking_config = setup_tracking_for_tenant(config_dict)

                    if tracking_config.enabled:
                        tracking_service = get_tracking_service()
                        recent_events = tracking_service.db.get_recent_events(tenant_name, limit=10)
                        week_events = tracking_service.db.get_events_by_date_range(tenant_name, week_ago, today)
                except Exception as tracking_error:
                    print(f"⚠️ Warning: Could not get tracking data for {tenant_name}: {tracking_error}")
                    # Continue without tracking data rather than failing

                # Calculate metrics - only count full_processing events to avoid duplicates
                full_processing_events = [e for e in week_events if e.operation_type.value == 'full_processing']
                total_week = len(full_processing_events)
                successful_week = len([e for e in full_processing_events if e.status.value == 'success'])
                failed_week = len([e for e in full_processing_events if e.status.value in ['failed', 'error']])

                # Get last activity
                last_activity = recent_events[0].processing_date if recent_events else None

                # Determine status based on available data
                if last_activity and (datetime.now() - last_activity).days < 1:
                    status = 'active'
                elif len(mailboxes) > 0:
                    status = 'inactive'  # Has mailboxes but no recent activity
                else:
                    status = 'inactive'  # No mailboxes configured

                # Check if tracking is enabled for this tenant
                tracking_enabled = False
                try:
                    from core.tracking.config import setup_tracking_for_tenant
                    tracking_config = setup_tracking_for_tenant(config_dict)
                    tracking_enabled = tracking_config.enabled
                except:
                    pass

                tenant_activity.append({
                    'tenant_name': tenant_name,
                    'storage_type': "key_vault" if creds_source == "key_vault" else "file",
                    'mailbox_count': len(mailboxes),
                    'documents_this_week': total_week,
                    'successful_this_week': successful_week,
                    'failed_this_week': failed_week,
                    'success_rate': (successful_week / total_week * 100) if total_week > 0 else 0,
                    'last_activity': last_activity.isoformat() if last_activity else None,
                    'status': status,
                    'preferred_language': config_dict.get('defaults', {}).get('preferred_language', 'English') if config_dict else 'English',
                    'tracking_enabled': tracking_enabled
                })

            except Exception as e:
                print(f"❌ Error processing tenant {tenant_name}: {e}")
                import traceback
                traceback.print_exc()

                # Even if there's an error, try to provide basic tenant info
                mailboxes = config_dict.get('mailboxes', {}) if config_dict else {}
                tenant_activity.append({
                    'tenant_name': tenant_name,
                    'storage_type': "key_vault" if creds_source == "key_vault" else "file",
                    'mailbox_count': len(mailboxes),
                    'documents_this_week': 0,
                    'successful_this_week': 0,
                    'failed_this_week': 0,
                    'success_rate': 0,
                    'last_activity': None,
                    'status': 'inactive',  # Changed from 'error' to 'inactive'
                    'preferred_language': config_dict.get('defaults', {}).get('preferred_language', 'English') if config_dict else 'English',
                    'tracking_enabled': False
                })

        return jsonify(tenant_activity)

    except Exception as e:
        print(f"❌ Error getting tenant activity: {e}")
        return jsonify([])

@app.route('/api/admin/tenants')
def admin_tenants():
    """Get list of all tenants for admin interface"""
    tenants = list_tenants()
    tenant_list = []
    
    for tenant_name, creds_source, config, token_cache_source in tenants:
        # Get mailbox info
        mailbox_manager = MailboxConfigManager(config)
        mailboxes = []
        
        if mailbox_manager.has_multi_mailbox_config():
            for mailbox in mailbox_manager.get_enabled_mailboxes():
                mailboxes.append({
                    "email": mailbox.email,
                    "display_name": mailbox.display_name,
                    "enabled": mailbox.enabled
                })
        
        # Get recent stats
        recent_docs = 0
        try:
            # Initialize tracking for this specific tenant
            from core.tracking.config import setup_tracking_for_tenant
            tracking_config = setup_tracking_for_tenant(config)

            if tracking_config.enabled:
                tracking_service = get_tracking_service()
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=7)
                daily_stats = tracking_service.get_date_range_stats(tenant_name, start_date, end_date)
                recent_docs = sum(stat.total_documents for stat in daily_stats)
        except:
            pass
        
        tenant_list.append({
            "name": tenant_name,
            "storage_type": "key_vault" if creds_source == "key_vault" else "file",
            "mailboxes": mailboxes,
            "mailbox_count": len(mailboxes),
            "recent_documents": recent_docs,
            "preferred_language": config.get("defaults", {}).get("preferred_language", "English"),
            "tracking_enabled": config.get("defaults", {}).get("tracking", {}).get("enabled", False)
        })
    
    return jsonify(tenant_list)

@app.route('/api/admin/processing/start', methods=['POST'])
def start_processing():
    """Start background email processing"""
    global processing_thread, processing_active
    
    if processing_active:
        return jsonify({"error": "Processing already active"}), 400
    
    processing_active = True
    processing_thread = threading.Thread(target=background_email_processor, daemon=True)
    processing_thread.start()
    
    return jsonify({"message": "Background processing started"})

@app.route('/api/admin/processing/stop', methods=['POST'])
def stop_processing():
    """Stop background email processing"""
    global processing_active
    
    processing_active = False
    return jsonify({"message": "Background processing stopped"})

@app.route('/api/admin/processing-queue')
def admin_processing_queue():
    """Get current processing queue status"""
    try:
        # Get development mode
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        # This would integrate with your actual processing queue
        # For now, we'll simulate queue data based on recent activity
        tracking_service = get_tracking_service()

        if not tracking_service.is_enabled():
            return jsonify({
                'queue_size': 0,
                'processing_items': [],
                'completed_today': 0,
                'failed_today': 0,
                'average_processing_time': 0
            })

        tenants = list_tenants()
        if dev_mode:
            dev_tenants = [t for t in tenants if 'prototype' in t[0].lower() or 'dev' in t[0].lower() or 'test' in t[0].lower()]
            if not dev_tenants and tenants:
                dev_tenants = [tenants[0]]
            tenants = dev_tenants

        # Get today's processing data
        from datetime import datetime
        today = datetime.now().date()

        completed_today = 0
        failed_today = 0
        processing_times = []

        for tenant_name, _, _, _ in tenants:
            try:
                events = tracking_service.database.get_events_by_date_range(tenant_name, today, today)
                for event in events:
                    if event.status.value == 'SUCCESS':
                        completed_today += 1
                    elif event.status.value in ['FAILED', 'ERROR']:
                        failed_today += 1

                    if event.processing_time_ms:
                        processing_times.append(event.processing_time_ms)
            except:
                continue

        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0

        return jsonify({
            'queue_size': 0,  # No items currently queued
            'processing_items': [],  # No items currently processing
            'completed_today': completed_today,
            'failed_today': failed_today,
            'average_processing_time': round(avg_processing_time, 2)
        })

    except Exception as e:
        print(f"❌ Error getting processing queue: {e}")
        return jsonify({
            'queue_size': 0,
            'processing_items': [],
            'completed_today': 0,
            'failed_today': 0,
            'average_processing_time': 0
        })

@app.route('/api/admin/error-logs')
def admin_error_logs():
    """Get recent error logs"""
    tracking_service = get_tracking_service()
    if not tracking_service.is_enabled():
        return jsonify([])

    try:
        # Get development mode
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        tenants = list_tenants()
        if dev_mode:
            dev_tenants = [t for t in tenants if 'prototype' in t[0].lower() or 'dev' in t[0].lower() or 'test' in t[0].lower()]
            if not dev_tenants and tenants:
                dev_tenants = [tenants[0]]
            tenants = dev_tenants

        error_logs = []

        for tenant_name, _, _, _ in tenants:
            try:
                # Get recent events with errors
                recent_events = tracking_service.database.get_recent_events(tenant_name, limit=50)
                for event in recent_events:
                    if event.status.value in ['FAILED', 'ERROR'] and event.error_message:
                        error_logs.append({
                            'id': f"{tenant_name}_{event.filename}_{event.processing_date.isoformat()}",
                            'tenant': tenant_name,
                            'filename': event.filename,
                            'document_type': event.document_type,
                            'error_message': event.error_message,
                            'timestamp': event.processing_date.isoformat(),
                            'mailbox': event.mailbox_email,
                            'severity': 'error' if event.status.value == 'ERROR' else 'warning'
                        })
            except:
                continue

        # Sort by timestamp (most recent first)
        error_logs.sort(key=lambda x: x['timestamp'], reverse=True)

        return jsonify(error_logs[:20])  # Return last 20 errors

    except Exception as e:
        print(f"❌ Error getting error logs: {e}")
        return jsonify([])

@app.route('/api/admin/processing/status')
def processing_status():
    """Get current processing status"""
    return jsonify({
        "active": processing_active,
        "thread_alive": processing_thread.is_alive() if processing_thread else False
    })

@app.route('/api/config/save', methods=['POST'])
def save_config():
    """Save configuration changes to config.json"""
    try:
        # Get development mode
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        # Only allow config changes in development mode for now
        if not dev_mode:
            return jsonify({'error': 'Configuration changes only allowed in development mode'}), 403

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No configuration data provided'}), 400

        print(f"📝 Received config data: {data}")

        # Get the prototype tenant (development tenant)
        tenants = list_tenants()
        dev_tenants = filter_tenants_by_mode(tenants, True)

        if not dev_tenants:
            return jsonify({'error': 'No development tenant found'}), 404

        tenant_name, creds_source, current_config, token_cache_source = dev_tenants[0]

        # Update the configuration
        updated_config = current_config.copy() if current_config else {}

        # Handle different configuration sections
        if 'mailboxes' in data:
            updated_config['mailboxes'] = data['mailboxes']

        if 'document_types' in data:
            updated_config['document_types'] = data['document_types']

        if 'defaults' in data:
            # Handle the entire defaults section
            if 'defaults' not in updated_config:
                updated_config['defaults'] = {}
            updated_config['defaults'].update(data['defaults'])

        if 'storage' in data:
            if 'defaults' not in updated_config:
                updated_config['defaults'] = {}
            updated_config['defaults']['subfolder_format'] = data['storage'].get('subfolder_format', '{doc_type}/{document_year}/{company_name}')

        if 'notifications' in data:
            if 'defaults' not in updated_config:
                updated_config['defaults'] = {}
            updated_config['defaults']['preferred_language'] = data['notifications'].get('preferred_language', 'English')

        # Save the updated configuration
        import json
        import os

        config_path = f"tenants/{tenant_name}/config.json"
        os.makedirs(os.path.dirname(config_path), exist_ok=True)

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(updated_config, f, indent=2, ensure_ascii=False)

        print(f"✅ Configuration saved for tenant {tenant_name}")

        return jsonify({
            'success': True,
            'message': 'Configuration saved successfully',
            'tenant': tenant_name
        })

    except Exception as e:
        print(f"❌ Error saving configuration: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to save configuration: {str(e)}'}), 500

@app.route('/api/config/load')
def load_config():
    """Load current configuration from config.json"""
    try:
        # Get development mode
        dev_mode = request.args.get('dev_mode', 'false').lower() == 'true'

        # Get the appropriate tenant
        tenants = list_tenants()
        if dev_mode:
            dev_tenants = filter_tenants_by_mode(tenants, True)
            if not dev_tenants:
                return jsonify({'error': 'No development tenant found'}), 404
            tenant_name, creds_source, current_config, token_cache_source = dev_tenants[0]
        else:
            # For production mode, we might want to handle multiple tenants differently
            # For now, just return empty config for production
            return jsonify({
                'mailboxes': {},
                'document_types': {},
                'defaults': {
                    'storage': {'subfolder_format': '{doc_type}/{document_year}/{company_name}'},
                    'notification': {'recipients': []},
                    'preferred_language': 'English',
                    'tracking': {'enabled': True},
                    'email_filtering': {'process_external_only': True}
                }
            })

        if not current_config:
            return jsonify({'error': 'No configuration found'}), 404

        # Return the current configuration
        return jsonify(current_config)

    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to load configuration: {str(e)}'}), 500

# ============================================================================
# SERVE FRONTEND FILES
# ============================================================================

@app.route('/')
def serve_frontend():
    """Serve the main frontend application"""
    if DEVELOPMENT_MODE:
        # In development, check if built files exist
        dist_path = 'core/web_portal/dist'
        if os.path.exists(dist_path):
            return send_from_directory(dist_path, 'index.html')
        else:
            # Fallback to simple development dashboard
            return render_template_string(SIMPLE_DEV_DASHBOARD)
    else:
        # Production mode - serve built React app
        return send_from_directory('core/web_portal/dist', 'index.html')

@app.route('/<path:path>')
def serve_static(path):
    """Serve static frontend files"""
    try:
        if DEVELOPMENT_MODE:
            dist_path = 'core/web_portal/dist'
            if os.path.exists(dist_path):
                return send_from_directory(dist_path, path)
            else:
                # For development without built files, return simple dashboard
                return serve_frontend()
        else:
            return send_from_directory('core/web_portal/dist', path)
    except:
        # For SPA routing, return index.html for unknown routes
        return serve_frontend()

if __name__ == '__main__':
    print("🚀 Starting Mail_Auto Web Server")
    print("📊 Dashboard: http://localhost:5000")
    print("🔧 Admin Panel: http://localhost:5000/admin")
    print("📡 API Health: http://localhost:5000/api/health")
    
    # Start the web server
    app.run(host='0.0.0.0', port=5000, debug=True)
